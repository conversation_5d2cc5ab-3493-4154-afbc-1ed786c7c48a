<!--- Thank you for contributing to rustypaste! -->

## Description

<!--- Describe your changes in detail -->

## Motivation and Context

<!--- Why is this change required? What problem does it solve? -->
<!--- If it fixes an open issue, please link to the issue here. -->

## How Has This Been Tested?

<!--- Please describe in detail how you tested your changes. -->
<!--- Include details of your testing environment, and the tests you ran to -->
<!--- see how your change affects other areas of the code, etc. -->

## Changelog Entry

<!--- Please write the changelog entry for these changes. -->
<!--- Follow the <https://keepachangelog.com/en/1.0.0/> format. -->
<!--- Use one of the Added, Changed, Deprecated, Removed, Fixed, and Security headers accordingly. -->
<!-- For example:
````
### Added

- Add a middleware for checking the content length
  - Before, the upload size was checked after full upload which was clearly wrong.
````
-->

## Types of Changes

<!--- What types of changes does your code introduce? Put an `x` in all the boxes that apply: -->

- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to change)
- [ ] Documentation (no code change)
- [ ] Refactor (refactoring production code)
- [ ] Other <!--- (provide information) -->

## Checklist:

<!--- Go over all the following points, and put an `x` in all the boxes that apply. -->

- [ ] My code follows the code style of this project.
- [ ] I have updated the documentation accordingly.
- [ ] I have formatted the code with [rustfmt](https://github.com/rust-lang/rustfmt).
- [ ] I checked the lints with [clippy](https://github.com/rust-lang/rust-clippy).
- [ ] I have added tests to cover my changes.
- [ ] All new and existing tests passed.
