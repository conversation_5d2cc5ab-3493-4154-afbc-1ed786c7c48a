<html lang="en">
  <head>
    <title>rustypaste</title>
    <meta charset="utf-8" />
    <style>
      body {
        background-color: #f2f2f2;
        font-family: arial, sans-serif;
        margin: 0;
        padding: 0;
      }
      pre {
        background-color: #333;
        color: #fff;
        font-size: 18px;
        margin: 0;
        overflow: auto;
        padding: 20px;
        white-space: pre-wrap;
      }
      h2 {
        color: #333;
        font-size: 24px;
        margin-top: 40px;
      }
      form {
        margin: 20px 0;
      }
      input[type="text"],
      input[type="file"] {
        border: none;
        border-radius: 5px;
        box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
        font-family: arial, sans-serif;
        font-size: 18px;
        margin-right: 10px;
        padding: 10px;
        width: 400px;
      }
      input[type="submit"] {
        background-color: #333;
        border: none;
        border-radius: 5px;
        box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
        color: #fff;
        cursor: pointer;
        font-family: arial, sans-serif;
        font-size: 18px;
        padding: 10px 20px;
        transition: background-color 0.2s ease-in-out;
      }
      input[type="submit"]:hover {
        background-color: #444;
      }
    </style>
  </head>
  <body>
    <pre>
┬─┐┬ ┬┌─┐┌┬┐┬ ┬┌─┐┌─┐┌─┐┌┬┐┌─┐
├┬┘│ │└─┐ │ └┬┘├─┘├─┤└─┐ │ ├┤
┴└─└─┘└─┘ ┴  ┴ ┴  ┴ ┴└─┘ ┴ └─┘
the server administrator might remove any pastes that they do not personally
want to host.

by default, pastes expire every hour.
        </pre
    >
    <h2>share url</h2>
    <form action="/" method="post" enctype="multipart/form-data">
      <input type="text" name="url" />
      <input type="submit" value="share" />
    </form>

    <h2>share file from url</h2>
    <form action="/" method="post" enctype="multipart/form-data">
      <input type="text" name="remote" />
      <input type="submit" value="share" />
    </form>

    <h2>share file</h2>
    <form action="/" method="post" enctype="multipart/form-data">
      <input type="file" name="file" />
      <input type="submit" value="share" />
    </form>

    <h2>share one-time file</h2>
    <form action="/" method="post" enctype="multipart/form-data">
      <input type="file" name="oneshot" />
      <input type="submit" value="share" />
    </form>

    <h2>share file with auth token</h2>
    <div id="shareForm">
      <input type="file" id="file" name="file" /><br>
      <input type="text" id="authToken" placeholder="Auth Token" />
      <input type="submit" value="share" onclick="shareFileWithAuth()" />
    </form>
  </body>

  <script>
    function shareFileWithAuth() {
      const fileInput = document.getElementById("file");
      const file = fileInput.files[0];

      if (!file) {
        alert("Please select a file");
        return;
      }

      const authTokenInput = document.getElementById("authToken");
      const authToken = authTokenInput.value;

      if (!authToken) {
        alert("Please provide an Auth Token");
        return;
      }

      const formData = new FormData();
      formData.append("file", file);

      fetch("/", {
        method: "POST",
        headers: {
          Authorization: authToken,
        },
        body: formData,
      })
        .then((response) => {
          if (!response.ok) {
            alert("Failed to upload");
            throw new Error("Network response was not ok");
          }
          return response.text();
        })
        .then((data) => {
          window.open(data, "_blank");
        })
        .catch((error) => {
          console.error("There was an error uploading the file:", error);
        });
    }
  </script>
</html>
