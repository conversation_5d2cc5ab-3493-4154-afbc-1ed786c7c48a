use prometheus::{
    Gauge, Histogram, HistogramOpts, IntCounter, IntGauge, Opts, Registry,
};
use std::sync::Arc;
use sysinfo::{System, Disks};
use std::path::Path;

/// Metrics collector for rustypaste
#[derive(<PERSON><PERSON>, Debug)]
pub struct Metrics {
    pub registry: Arc<Registry>,
    
    // Request metrics
    pub http_requests_total: IntCounter,
    pub http_request_duration: Histogram,
    pub http_requests_in_flight: IntGauge,
    
    // File operation metrics
    pub file_uploads_total: IntCounter,
    pub file_downloads_total: IntCounter,
    pub file_deletions_total: IntCounter,
    pub file_upload_size_bytes: Histogram,
    pub file_download_size_bytes: Histogram,
    
    // Storage metrics
    pub storage_used_bytes: IntGauge,
    pub storage_available_bytes: IntGauge,
    pub files_stored_total: IntGauge,
    
    // Authentication metrics
    pub auth_attempts_total: IntCounter,
    pub auth_failures_total: IntCounter,
    
    // System metrics
    pub memory_usage_bytes: IntGauge,
    pub cpu_usage_percent: Gauge,
}

impl Metrics {
    /// Create a new metrics instance
    pub fn new() -> Result<Self, prometheus::Error> {
        let registry = Arc::new(Registry::new());
        
        // HTTP request metrics
        let http_requests_total = IntCounter::with_opts(Opts::new(
            "rustypaste_http_requests_total",
            "Total number of HTTP requests processed"
        ))?;
        
        let http_request_duration = Histogram::with_opts(HistogramOpts::new(
            "rustypaste_http_request_duration_seconds",
            "HTTP request duration in seconds"
        ))?;
        
        let http_requests_in_flight = IntGauge::with_opts(Opts::new(
            "rustypaste_http_requests_in_flight",
            "Number of HTTP requests currently being processed"
        ))?;
        
        // File operation metrics
        let file_uploads_total = IntCounter::with_opts(Opts::new(
            "rustypaste_file_uploads_total",
            "Total number of files uploaded"
        ))?;
        
        let file_downloads_total = IntCounter::with_opts(Opts::new(
            "rustypaste_file_downloads_total",
            "Total number of files downloaded"
        ))?;
        
        let file_deletions_total = IntCounter::with_opts(Opts::new(
            "rustypaste_file_deletions_total",
            "Total number of files deleted"
        ))?;
        
        let file_upload_size_bytes = Histogram::with_opts(HistogramOpts::new(
            "rustypaste_file_upload_size_bytes",
            "Size of uploaded files in bytes"
        ))?;
        
        let file_download_size_bytes = Histogram::with_opts(HistogramOpts::new(
            "rustypaste_file_download_size_bytes",
            "Size of downloaded files in bytes"
        ))?;
        
        // Storage metrics
        let storage_used_bytes = IntGauge::with_opts(Opts::new(
            "rustypaste_storage_used_bytes",
            "Storage space used in bytes"
        ))?;
        
        let storage_available_bytes = IntGauge::with_opts(Opts::new(
            "rustypaste_storage_available_bytes",
            "Storage space available in bytes"
        ))?;
        
        let files_stored_total = IntGauge::with_opts(Opts::new(
            "rustypaste_files_stored_total",
            "Total number of files currently stored"
        ))?;
        
        // Authentication metrics
        let auth_attempts_total = IntCounter::with_opts(Opts::new(
            "rustypaste_auth_attempts_total",
            "Total number of authentication attempts"
        ))?;
        
        let auth_failures_total = IntCounter::with_opts(Opts::new(
            "rustypaste_auth_failures_total",
            "Total number of authentication failures"
        ))?;
        
        // System metrics
        let memory_usage_bytes = IntGauge::with_opts(Opts::new(
            "rustypaste_memory_usage_bytes",
            "Memory usage in bytes"
        ))?;
        
        let cpu_usage_percent = Gauge::with_opts(Opts::new(
            "rustypaste_cpu_usage_percent",
            "CPU usage percentage"
        ))?;
        
        // Register all metrics
        registry.register(Box::new(http_requests_total.clone()))?;
        registry.register(Box::new(http_request_duration.clone()))?;
        registry.register(Box::new(http_requests_in_flight.clone()))?;
        registry.register(Box::new(file_uploads_total.clone()))?;
        registry.register(Box::new(file_downloads_total.clone()))?;
        registry.register(Box::new(file_deletions_total.clone()))?;
        registry.register(Box::new(file_upload_size_bytes.clone()))?;
        registry.register(Box::new(file_download_size_bytes.clone()))?;
        registry.register(Box::new(storage_used_bytes.clone()))?;
        registry.register(Box::new(storage_available_bytes.clone()))?;
        registry.register(Box::new(files_stored_total.clone()))?;
        registry.register(Box::new(auth_attempts_total.clone()))?;
        registry.register(Box::new(auth_failures_total.clone()))?;
        registry.register(Box::new(memory_usage_bytes.clone()))?;
        registry.register(Box::new(cpu_usage_percent.clone()))?;
        
        Ok(Metrics {
            registry,
            http_requests_total,
            http_request_duration,
            http_requests_in_flight,
            file_uploads_total,
            file_downloads_total,
            file_deletions_total,
            file_upload_size_bytes,
            file_download_size_bytes,
            storage_used_bytes,
            storage_available_bytes,
            files_stored_total,
            auth_attempts_total,
            auth_failures_total,
            memory_usage_bytes,
            cpu_usage_percent,
        })
    }
    
    /// Update storage metrics based on upload directory
    pub fn update_storage_metrics(&self, upload_path: &Path) -> Result<(), std::io::Error> {
        let disks = Disks::new_with_refreshed_list();

        // Update disk usage
        if let Some(disk) = disks.iter().find(|d| {
            upload_path.starts_with(d.mount_point())
        }) {
            self.storage_available_bytes.set(disk.available_space() as i64);
            self.storage_used_bytes.set((disk.total_space() - disk.available_space()) as i64);
        }
        
        // Count files in upload directory
        let file_count = count_files_recursive(upload_path)?;
        self.files_stored_total.set(file_count as i64);
        
        Ok(())
    }
    
    /// Update system metrics
    pub fn update_system_metrics(&self) {
        let mut system = System::new_all();
        system.refresh_all();

        // Update memory usage
        self.memory_usage_bytes.set(system.used_memory() as i64);

        // Update CPU usage (average across all cores)
        let cpu_usage: f64 = system.cpus().iter()
            .map(|cpu| cpu.cpu_usage() as f64)
            .sum::<f64>() / system.cpus().len() as f64;
        self.cpu_usage_percent.set(cpu_usage);
    }
    
    /// Get metrics as Prometheus format string
    pub fn gather(&self) -> String {
        let encoder = prometheus::TextEncoder::new();
        let metric_families = self.registry.gather();
        encoder.encode_to_string(&metric_families).unwrap_or_default()
    }
}

impl Default for Metrics {
    fn default() -> Self {
        Self::new().expect("Failed to create metrics")
    }
}

/// Recursively count files in a directory
fn count_files_recursive(dir: &Path) -> Result<usize, std::io::Error> {
    let mut count = 0;
    
    if dir.is_dir() {
        for entry in std::fs::read_dir(dir)? {
            let entry = entry?;
            let path = entry.path();
            
            if path.is_dir() {
                count += count_files_recursive(&path)?;
            } else {
                count += 1;
            }
        }
    }
    
    Ok(count)
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;
    use std::fs;

    #[test]
    fn test_metrics_creation() {
        let metrics = Metrics::new();
        assert!(metrics.is_ok());
    }

    #[test]
    fn test_metrics_gather() {
        let metrics = Metrics::new().unwrap();
        let output = metrics.gather();
        
        assert!(output.contains("rustypaste_http_requests_total"));
        assert!(output.contains("rustypaste_file_uploads_total"));
        assert!(output.contains("rustypaste_storage_used_bytes"));
    }

    #[test]
    fn test_count_files_recursive() -> Result<(), std::io::Error> {
        let temp_dir = TempDir::new()?;
        let dir_path = temp_dir.path();
        
        // Create some test files
        fs::write(dir_path.join("file1.txt"), "content1")?;
        fs::write(dir_path.join("file2.txt"), "content2")?;
        
        // Create subdirectory with files
        let sub_dir = dir_path.join("subdir");
        fs::create_dir(&sub_dir)?;
        fs::write(sub_dir.join("file3.txt"), "content3")?;
        
        let count = count_files_recursive(dir_path)?;
        assert_eq!(count, 3);
        
        Ok(())
    }

    #[test]
    fn test_storage_metrics_update() -> Result<(), std::io::Error> {
        let metrics = Metrics::new().unwrap();
        let temp_dir = TempDir::new()?;
        
        // Create some test files
        fs::write(temp_dir.path().join("test1.txt"), "test content")?;
        fs::write(temp_dir.path().join("test2.txt"), "more content")?;
        
        metrics.update_storage_metrics(temp_dir.path())?;
        
        // Files should be counted
        assert_eq!(metrics.files_stored_total.get(), 2);
        
        Ok(())
    }

    #[test]
    fn test_system_metrics_update() {
        let metrics = Metrics::new().unwrap();
        metrics.update_system_metrics();
        
        // Should have some memory usage
        assert!(metrics.memory_usage_bytes.get() > 0);
        
        // CPU usage should be between 0 and 100
        let cpu_usage = metrics.cpu_usage_percent.get();
        assert!(cpu_usage >= 0.0 && cpu_usage <= 100.0);
    }
}
