# Rustypaste Enhancement - Implementation Deliverables & Testing Commands

## Overview
This document contains all the commands, tests, and deliverables for the rustypaste enhancement project. Use these commands to demonstrate the implemented features and take screenshots for your submission.

## Prerequisites Setup

### 1. Install Rust and Cargo (if not already installed)
```bash
# Install Rust
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
source ~/.cargo/env

# Verify installation
rustc --version
cargo --version
```

### 2. Install Additional Testing Tools
```bash
# Install coverage tool
cargo install cargo-tarpaulin

# Install audit tool for security scanning
cargo install cargo-audit

# Install clippy for linting (usually included with Rust)
rustup component add clippy

# Install rustfmt for formatting
rustup component add rustfmt
```

## Project Setup Commands

### 1. Navigate to Project Directory
```bash
cd c:\dev\rustypaste
```

### 2. Build the Project
```bash
# Clean build
cargo clean

# Build in debug mode
cargo build

# Build in release mode (for performance testing)
cargo build --release
```

## Testing Commands (For Screenshots)

### 1. Unit Tests Execution
```bash
# Run all tests with output
cargo test -- --nocapture

# Run tests with single thread (for consistency)
cargo test -- --test-threads 1

# Run specific module tests
cargo test config::tests
cargo test auth::tests
cargo test paste::tests
cargo test util::tests
```

### 2. Integration Tests
```bash
# Run integration tests specifically
cargo test --test integration_tests

# Run with verbose output
cargo test --test integration_tests -- --nocapture
```

### 3. Test Coverage Analysis
```bash
# Generate coverage report
cargo tarpaulin --out Html --output-dir coverage

# Generate coverage with detailed output
cargo tarpaulin --verbose --out Html --output-dir coverage

# View coverage summary
cargo tarpaulin --out Stdout
```

### 4. Code Quality Checks
```bash
# Run Clippy linting
cargo clippy -- -D warnings

# Run formatting check
cargo fmt -- --check

# Security audit
cargo audit

# Check for unused dependencies
cargo +nightly udeps
```

## Application Testing Commands

### 1. Start the Server
```bash
# Start server with default config
cargo run

# Start server with custom config
CONFIG=./fixtures/test-server-auto-deletion/config.toml cargo run

# Start in release mode for performance testing
cargo run --release
```

### 2. Health Check Endpoints Testing
```bash
# Test health endpoint
curl -i http://127.0.0.1:8000/health

# Test readiness endpoint
curl -i http://127.0.0.1:8000/ready

# Test metrics endpoint
curl -i http://127.0.0.1:8000/metrics

# Test version endpoint
curl -i http://127.0.0.1:8000/version
```

### 3. File Upload Testing
```bash
# Basic file upload
echo "Test content" > test.txt
curl -F "file=@test.txt" http://127.0.0.1:8000/

# Upload with authentication
curl -F "file=@test.txt" -H "Authorization: Bearer test_token" http://127.0.0.1:8000/

# Upload with custom filename
curl -F "file=@test.txt" -H "filename: custom_name.txt" http://127.0.0.1:8000/

# URL shortening
curl -F "url=https://example.com/very/long/url" http://127.0.0.1:8000/
```

### 4. Rate Limiting Testing
```bash
# Test rate limiting (run multiple times quickly)
for i in {1..70}; do
  echo "Request $i"
  curl -s -o /dev/null -w "%{http_code}\n" http://127.0.0.1:8000/health
  sleep 0.1
done
```

### 5. Security Headers Testing
```bash
# Check security headers
curl -I http://127.0.0.1:8000/

# Test with file upload to see all headers
curl -I -F "file=@test.txt" http://127.0.0.1:8000/
```

## Performance Testing Commands

### 1. Load Testing with Apache Bench (if available)
```bash
# Install apache bench
# On Ubuntu: sudo apt-get install apache2-utils
# On macOS: brew install httpie

# Basic load test
ab -n 1000 -c 10 http://127.0.0.1:8000/health

# Metrics endpoint load test
ab -n 100 -c 5 http://127.0.0.1:8000/metrics
```

### 2. Memory and CPU Monitoring
```bash
# Monitor resource usage while running
top -p $(pgrep rustypaste)

# Or use htop for better visualization
htop -p $(pgrep rustypaste)
```

## Metrics and Monitoring Validation

### 1. Prometheus Metrics Validation
```bash
# Get all metrics
curl -s http://127.0.0.1:8000/metrics | grep rustypaste

# Check specific metrics
curl -s http://127.0.0.1:8000/metrics | grep "rustypaste_http_requests_total"
curl -s http://127.0.0.1:8000/metrics | grep "rustypaste_file_uploads_total"
curl -s http://127.0.0.1:8000/metrics | grep "rustypaste_storage_used_bytes"
```

### 2. Health Check Validation
```bash
# JSON formatted health check
curl -s http://127.0.0.1:8000/health | jq .

# Check readiness
curl -s http://127.0.0.1:8000/ready | jq .
```

## Security Testing Commands

### 1. Input Validation Testing
```bash
# Test filename sanitization
curl -F "file=@test.txt" -H "filename: ../../../etc/passwd" http://127.0.0.1:8000/

# Test with suspicious content
echo "<script>alert('xss')</script>" > malicious.txt
curl -F "file=@malicious.txt" http://127.0.0.1:8000/

# Test with large filename
curl -F "file=@test.txt" -H "filename: $(python3 -c 'print("a"*300)')" http://127.0.0.1:8000/
```

### 2. Rate Limiting Validation
```bash
# Rapid requests to trigger rate limiting
seq 1 100 | xargs -I {} -P 10 curl -s -o /dev/null -w "%{http_code} " http://127.0.0.1:8000/health; echo
```

## Documentation Generation

### 1. Generate Documentation
```bash
# Generate Rust documentation
cargo doc --open

# Generate documentation for dependencies too
cargo doc --document-private-items --open
```

## File Structure Validation

### 1. Check Implementation Files
```bash
# List all modified/created files
find src/ -name "*.rs" -exec wc -l {} + | sort -n

# Check test files
find tests/ -name "*.rs" -exec wc -l {} +

# Verify Cargo.toml dependencies
cat Cargo.toml | grep -A 20 "\[dependencies\]"
```

## Cleanup Commands

### 1. Clean Test Artifacts
```bash
# Remove test files
rm -f test.txt malicious.txt

# Clean build artifacts
cargo clean

# Remove coverage reports
rm -rf coverage/
```

## Screenshot Checklist

For your submission, take screenshots of:

1. **Test Results**: `cargo test -- --nocapture`
2. **Coverage Report**: `cargo tarpaulin --out Stdout`
3. **Health Endpoints**: `curl http://127.0.0.1:8000/health`
4. **Metrics Output**: `curl http://127.0.0.1:8000/metrics`
5. **Rate Limiting**: Multiple rapid requests showing 429 responses
6. **Security Headers**: `curl -I http://127.0.0.1:8000/`
7. **Code Quality**: `cargo clippy` output
8. **Build Success**: `cargo build --release` output

## Troubleshooting

### Common Issues:
1. **Port already in use**: Change port in config.toml or kill existing process
2. **Permission denied**: Ensure upload directory is writable
3. **Dependency issues**: Run `cargo update` to update dependencies
4. **Test failures**: Ensure no other rustypaste instance is running

### Debug Commands:
```bash
# Check if port is in use
netstat -tulpn | grep :8000

# Kill existing process
pkill rustypaste

# Check logs
RUST_LOG=debug cargo run
```
