name: Continuous Integration

on:
  push:
    branches:
      - master
  pull_request:
    branches:
      - master
  schedule:
    - cron: "0 0 * * 0"

jobs:
  check:
    name: Check
    runs-on: ubuntu-22.04
    steps:
      - name: Checkout the repository
        uses: actions/checkout@v4
      - name: Install Rust toolchain
        uses: dtolnay/rust-toolchain@stable
      - name: Check the project files
        run: cargo check --locked --verbose

  test:
    name: Test suite
    runs-on: ubuntu-22.04
    steps:
      - name: Checkout the repository
        uses: actions/checkout@v4
      - name: Install Rust toolchain
        uses: dtolnay/rust-toolchain@stable
      - name: Install cargo-llvm-cov
        uses: taiki-e/install-action@cargo-llvm-cov
      - name: Generate code coverage
        run: cargo llvm-cov --lcov --output-path lcov.info -- --test-threads 1
        env:
          OUT_DIR: target
      - name: Upload reports to codecov
        run: |
          curl -Os https://uploader.codecov.io/latest/linux/codecov
          chmod +x codecov
          ./codecov -t ${CODECOV_TOKEN} -f lcov.info
        env:
          CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}

  fixtures:
    strategy:
      fail-fast: false
      matrix:
        os:
          - name: Linux
            runner: ubuntu-22.04
          - name: macOS
            runner: macos-14
    name: Test fixtures [${{ matrix.os.name }}]
    runs-on: ${{ matrix.os.runner }}
    steps:
      - name: Checkout the repository
        uses: actions/checkout@v4
      - name: Install coreutils for MacOS
        if: matrix.os.name == 'macOS'
        run: brew install coreutils
      - name: Install Rust toolchain
        uses: dtolnay/rust-toolchain@stable
      - name: Build the project
        run: cargo build --locked --verbose
      - name: Run test fixtures
        shell: bash
        run: ./test-fixtures.sh
        working-directory: fixtures
        env:
          DEBUG: true

  clippy:
    name: Lints
    runs-on: ubuntu-22.04
    steps:
      - name: Checkout the repository
        uses: actions/checkout@v4
      - name: Install Rust toolchain
        uses: dtolnay/rust-toolchain@stable
        with:
          components: clippy
      - name: Check the lints
        run: cargo clippy --tests --verbose -- -D warnings

  rustfmt:
    name: Formatting
    runs-on: ubuntu-22.04
    steps:
      - name: Checkout the repository
        uses: actions/checkout@v4
      - name: Install Rust toolchain
        uses: dtolnay/rust-toolchain@stable
        with:
          components: rustfmt
      - name: Check the formatting
        run: cargo fmt -- --check --verbose

  lychee:
    name: Links
    runs-on: ubuntu-22.04
    steps:
      - name: Checkout the repository
        uses: actions/checkout@v4
      - name: Check the links
        uses: lycheeverse/lychee-action@v2
        with:
          args: -v *.md
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
