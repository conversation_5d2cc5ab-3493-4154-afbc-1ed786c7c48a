name: Continuous Deployment

on:
  push:
    tags:
      - "v*.*.*"

jobs:
  publish-github:
    name: Publish on GitHub
    runs-on: ubuntu-22.04
    strategy:
      matrix:
        TARGET: [x86_64-unknown-linux-gnu, x86_64-unknown-linux-musl]
    steps:
      - name: Checkout the repository
        uses: actions/checkout@v4
      - name: Set the release version
        run: echo "RELEASE_VERSION=${GITHUB_REF:11}" >> $GITHUB_ENV
      - name: Install musl-tools
        if: matrix.TARGET == 'x86_64-unknown-linux-musl'
        run: |
          sudo apt-get update
          sudo apt-get install -y --no-install-recommends \
            --allow-unauthenticated musl-tools
      - name: Install Rust toolchain
        uses: dtolnay/rust-toolchain@stable
        with:
          target: ${{ matrix.TARGET }}
      - name: Build
        run: |
          cargo build --release --locked --target ${{ matrix.TARGET }}
          strip target/${{ matrix.TARGET }}/release/rustypaste
      - name: Prepare release assets
        run: |
          mkdir release/
          cp {LICENSE,README.md,CHANGELOG.md,config.toml} release/
          cp target/${{ matrix.TARGET }}/release/rustypaste release/
          mv release/ rustypaste-${{ env.RELEASE_VERSION }}/
      - name: Create release artifacts
        run: |
          tar -czvf rustypaste-${{ env.RELEASE_VERSION }}-${{ matrix.TARGET }}.tar.gz \
            rustypaste-${{ env.RELEASE_VERSION }}/
          sha512sum rustypaste-${{ env.RELEASE_VERSION }}-${{ matrix.TARGET }}.tar.gz \
            > rustypaste-${{ env.RELEASE_VERSION }}-${{ matrix.TARGET }}.tar.gz.sha512
      - name: Upload the release
        uses: svenstaro/upload-release-action@v2
        with:
          repo_token: ${{ secrets.GITHUB_TOKEN }}
          file: rustypaste-${{ env.RELEASE_VERSION }}-${{ matrix.TARGET }}.tar.gz*
          file_glob: true
          overwrite: true
          tag: ${{ github.ref }}

  publish-crates-io:
    name: Publish on crates.io
    needs: publish-github
    runs-on: ubuntu-22.04
    steps:
      - name: Checkout the repository
        uses: actions/checkout@v4
      - name: Install Rust toolchain
        uses: dtolnay/rust-toolchain@stable
      - name: Publish
        run: cargo publish --locked --token ${{ secrets.CARGO_TOKEN }}
