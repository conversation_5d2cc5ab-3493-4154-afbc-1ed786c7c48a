[package]
name = "rustypaste"
version = "0.16.1"
edition = "2021"
description = "A minimal file upload/pastebin service"
authors = ["<PERSON><PERSON>ız <<EMAIL>>"]
license = "MIT"
readme = "README.md"
homepage = "https://github.com/orhun/rustypaste"
repository = "https://github.com/orhun/rustypaste"
keywords = ["paste", "pastebin", "upload"]
categories = ["web-programming::http-server"]
include = ["src/**/*", "Cargo.*", "LICENSE", "README.md", "CHANGELOG.md"]

[features]
default = ["rustls"]
openssl = ["actix-web/openssl", "awc/openssl"]
rustls = ["actix-web/rustls-0_21", "awc/rustls-0_21"]

[dependencies]
actix-web = { version = "4.11.0" }
actix-web-grants = { version = "4.1.2" }
actix-multipart = "0.7.2"
actix-files = "0.6.6"
awc = { version = "3.7.0" }
serde = "1.0.219"
futures-util = "0.3.31"
petname = { version = "2.0.2", default-features = false, features = [
  "default-rng",
  "default-words",
] }
rand = "0.9.2"
dotenvy = "0.15.7"
url = "2.5.7"
mime = "0.3.17"
regex = "1.11.2"
serde_regex = "1.1.0"
lazy-regex = "3.4.1"
humantime = "2.2.0"
humantime-serde = "1.1.1"
glob = "0.3.3"
ring = "0.17.14"
hotwatch = "0.5.0"
tokio = { version = "1.47.1", optional = true }
tracing = "0.1.41"
tracing-subscriber = { version = "0.3.19", features = ["env-filter"] }
uts2ts = "0.4.1"
path-clean = "1.0.1"


[dependencies.config]
version = "0.15.14"
default-features = false
features = ["toml", "yaml"]

[dependencies.byte-unit]
version = "5.1.6"
features = ["serde"]

[dependencies.infer]
version = "0.19.0"
default-features = false

[dev-dependencies]
actix-rt = "2.10.0"
tempfile = "3.8.1"
mockito = "1.2.0"
tokio-test = "0.4.3"
serial_test = "3.0.0"

[profile.dev]
opt-level = 0
debug = true

[profile.test]
opt-level = 0
debug = true

[profile.release]
opt-level = 3
debug = false
panic = "unwind"
lto = true
codegen-units = 1
strip = true

[profile.bench]
opt-level = 3
debug = false
