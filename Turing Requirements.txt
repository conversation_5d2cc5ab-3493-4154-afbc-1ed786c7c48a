Phase 2: Repository-Specific Prompt Generation

Core Task: Create Contextual Enhancement Prompts
Based on your repository analysis, generate 5 specific, actionable prompts that demonstrate a deep understanding of the codebase. Each prompt should be:

Repository-specific: Reference actual files, functions, or architectural patterns.

Technically precise: Use the project’s terminology and concepts.

Measurably impactful: Include clear success criteria and expected outcomes.

Prompt Development Process
Identify areas for improvement within the repository and create detailed enhancement prompts by considering:

What are the biggest pain points in the current implementation?

Where would improvements have the most significant impact?

What enhancements would other developers find most valuable?

Which changes would improve the project’s production readiness?



Prompt Quality Requirements
Each prompt must include:

Specific Context: Reference actual code locations, file names, or existing functions.

Technical Rationale: Explain why this improvement is needed based on your code analysis.

Implementation Scope: Define clear boundaries and deliverables.

Success Metrics: Specify measurable outcomes.

Integration Points: Describe how the prompt connects to the existing architecture.



Deliverable 2: Prompt Portfolio
Create a document containing:

5 detailed enhancement prompts based on your repository analysis.

Justifications for each prompt explaining why it’s valuable.

Priority ranking of the prompts with reasoning.

Implementation complexity assessment for each prompt.

Phase 3: Prompt-Driven Implementation

Implementation Process:

Prompt Selection: Choose 3 prompts from your generated set.

Implementation Planning: Develop a detailed technical approach for each prompt.

Execution: Implement solutions following the prompt specifications.

Validation: Verify that all success metrics are met.



Prompt Implementation Standards:
For each implemented prompt, ensure:

Exact Scope Delivery: Implement exactly what the prompt specifies.

Metric Achievement: Meet all defined success criteria.

Integration Verification: Ensure seamless integration with the existing codebase.

Documentation: Document the implementation approach and key decisions made.




Repository-Aware Prompting Standards:
Your implementations should demonstrate a thorough understanding of the repository by aligning with:

Existing Patterns: How the codebase currently handles similar functionality.

Naming Conventions: Consistency with the project’s variable, function, and class names.

Architecture Alignment: Fit naturally within the current system design.

Dependency Management: Work within existing dependencies and constraints.



Deliverable 3: Prompt Implementation Report
For each implemented prompt, provide documentation including:

Original Prompt: The exact prompt generated.

Implementation Approach: Explanation of how it was solved.

Code Changes: Key files modified and new files created.

Metrics Achieved: Evidence that success criteria were met.

Integration Testing: Description of how compatibility was verified.




Phase 4: Production Readiness & Quality Assurance

Code Quality & Testing Requirements

Unit Testing:

Write comprehensive unit tests for all new functionality.

Achieve over 90% test coverage for the implemented code.

Include tests for edge cases and error conditions.

Document test execution results with screenshots.




Static Analysis:

Run static code analysis tools suitable for your programming language.

Address all critical and high-severity issues identified.

Generate and document static analysis reports.

Configure automated static analysis within the Continuous Integration (CI) pipeline.




Code Linting & Formatting:

Consistently apply the project’s existing linting rules.

Fix all linting errors and warnings.

Ensure code formatting matches project standards.

Document linting configuration and results.




Final Deliverables

Enhanced Codebase:

Clean, well-documented code following project conventions.

Comprehensive test suite with over 90% coverage for new code.

Performance benchmarks demonstrating improvements.

Security audit documenting security considerations.




Quality Assurance Reports:

Unit test execution reports with coverage metrics.

Static analysis reports, including issue resolution documentation.

Linting and formatting compliance reports.

Deliverable Submission Structure

Create a folder in the designated Google Drive location named with your email ID.

Upload all required documents, reports, and the full codebase zip files for each implemented prompt into this folder.

Include the specified tracking sheet in the folder.