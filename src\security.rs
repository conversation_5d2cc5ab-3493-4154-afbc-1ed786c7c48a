use actix_web::dev::{forward_ready, Service, ServiceRequest, ServiceResponse, Transform};
use actix_web::http::header::{HeaderName, HeaderValue};
use actix_web::{Error, HttpResponse};
use futures_util::future::{ready, Ready};
use governor::{Quota, RateLimiter, clock::DefaultClock, state::keyed::DefaultKeyedStateStore};
use std::net::IpAddr;
use std::num::NonZeroU32;
use std::pin::Pin;
use std::rc::Rc;
use std::sync::Arc;
use std::future::Future;
use sha2::{Digest, Sha256};
use std::hash::DefaultHasher;

/// Rate limiting configuration
#[derive(Debug, Clone)]
pub struct RateLimitConfig {
    /// Maximum requests per minute per IP
    pub requests_per_minute: u32,
    /// Burst capacity
    pub burst_capacity: u32,
    /// Whether to enable rate limiting
    pub enabled: bool,
}

impl Default for RateLimitConfig {
    fn default() -> Self {
        Self {
            requests_per_minute: 60,
            burst_capacity: 10,
            enabled: true,
        }
    }
}

/// Security headers configuration
#[derive(Debug, Clone)]
pub struct SecurityHeadersConfig {
    /// Enable security headers
    pub enabled: bool,
    /// Content Security Policy
    pub csp: Option<String>,
    /// X-Frame-Options
    pub x_frame_options: String,
    /// X-Content-Type-Options
    pub x_content_type_options: String,
    /// Strict-Transport-Security
    pub hsts: Option<String>,
}

impl Default for SecurityHeadersConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            csp: Some("default-src 'self'".to_string()),
            x_frame_options: "DENY".to_string(),
            x_content_type_options: "nosniff".to_string(),
            hsts: Some("max-age=31536000; includeSubDomains".to_string()),
        }
    }
}

/// Rate limiting middleware
#[derive(Clone)]
pub struct RateLimitMiddleware {
    limiter: Arc<RateLimiter<IpAddr, DefaultHasher, DefaultClock, DefaultKeyedStateStore<IpAddr>>>,
    config: RateLimitConfig,
}

impl RateLimitMiddleware {
    /// Create a new rate limiting middleware
    pub fn new(config: RateLimitConfig) -> Self {
        let quota = Quota::per_minute(
            NonZeroU32::new(config.requests_per_minute).unwrap_or(NonZeroU32::new(60).unwrap())
        ).allow_burst(
            NonZeroU32::new(config.burst_capacity).unwrap_or(NonZeroU32::new(10).unwrap())
        );
        
        let limiter = Arc::new(RateLimiter::keyed(quota));
        
        Self { limiter, config }
    }
}

impl<S, B> Transform<S, ServiceRequest> for RateLimitMiddleware
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error> + 'static,
    S::Future: 'static,
    B: 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type Transform = RateLimitMiddlewareService<S>;
    type InitError = ();
    type Future = Ready<Result<Self::Transform, Self::InitError>>;

    fn new_transform(&self, service: S) -> Self::Future {
        ready(Ok(RateLimitMiddlewareService {
            service: Rc::new(service),
            limiter: Arc::clone(&self.limiter),
            config: self.config.clone(),
        }))
    }
}

pub struct RateLimitMiddlewareService<S> {
    service: Rc<S>,
    limiter: Arc<RateLimiter<IpAddr, DefaultHasher, DefaultClock, DefaultKeyedStateStore<IpAddr>>>,
    config: RateLimitConfig,
}

impl<S, B> Service<ServiceRequest> for RateLimitMiddlewareService<S>
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error> + 'static,
    S::Future: 'static,
    B: 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type Future = Pin<Box<dyn Future<Output = Result<Self::Response, Self::Error>>>>;

    forward_ready!(service);

    fn call(&self, req: ServiceRequest) -> Self::Future {
        if !self.config.enabled {
            let service = Rc::clone(&self.service);
            return Box::pin(async move { service.call(req).await });
        }

        let connection_info = req.connection_info().clone();
        let ip = connection_info
            .realip_remote_addr()
            .and_then(|addr| addr.parse::<IpAddr>().ok())
            .unwrap_or(IpAddr::from([127, 0, 0, 1]));

        let limiter = Arc::clone(&self.limiter);
        let service = Rc::clone(&self.service);

        Box::pin(async move {
            match limiter.check_key(&ip) {
                Ok(_) => service.call(req).await,
                Err(_) => {
                    warn!("Rate limit exceeded for IP: {}", ip);
                    let response = HttpResponse::TooManyRequests()
                        .json(serde_json::json!({
                            "error": "Rate limit exceeded",
                            "message": "Too many requests from this IP address"
                        }));
                    Ok(req.into_response(response).map_into_boxed_body())
                }
            }
        })
    }
}

/// Security headers middleware
#[derive(Clone)]
pub struct SecurityHeadersMiddleware {
    config: SecurityHeadersConfig,
}

impl SecurityHeadersMiddleware {
    /// Create a new security headers middleware
    pub fn new(config: SecurityHeadersConfig) -> Self {
        Self { config }
    }
}

impl<S, B> Transform<S, ServiceRequest> for SecurityHeadersMiddleware
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error> + 'static,
    S::Future: 'static,
    B: 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type Transform = SecurityHeadersMiddlewareService<S>;
    type InitError = ();
    type Future = Ready<Result<Self::Transform, Self::InitError>>;

    fn new_transform(&self, service: S) -> Self::Future {
        ready(Ok(SecurityHeadersMiddlewareService {
            service: Rc::new(service),
            config: self.config.clone(),
        }))
    }
}

pub struct SecurityHeadersMiddlewareService<S> {
    service: Rc<S>,
    config: SecurityHeadersConfig,
}

impl<S, B> Service<ServiceRequest> for SecurityHeadersMiddlewareService<S>
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error> + 'static,
    S::Future: 'static,
    B: 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type Future = Pin<Box<dyn Future<Output = Result<Self::Response, Self::Error>>>>;

    forward_ready!(service);

    fn call(&self, req: ServiceRequest) -> Self::Future {
        let service = Rc::clone(&self.service);
        let config = self.config.clone();

        Box::pin(async move {
            let mut response = service.call(req).await?;
            
            if config.enabled {
                let headers = response.headers_mut();
                
                // Add X-Frame-Options
                headers.insert(
                    HeaderName::from_static("x-frame-options"),
                    HeaderValue::from_str(&config.x_frame_options).unwrap_or_else(|_| HeaderValue::from_static("DENY"))
                );
                
                // Add X-Content-Type-Options
                headers.insert(
                    HeaderName::from_static("x-content-type-options"),
                    HeaderValue::from_str(&config.x_content_type_options).unwrap_or_else(|_| HeaderValue::from_static("nosniff"))
                );
                
                // Add Content-Security-Policy if configured
                if let Some(ref csp) = config.csp {
                    headers.insert(
                        HeaderName::from_static("content-security-policy"),
                        HeaderValue::from_str(csp).unwrap_or_else(|_| HeaderValue::from_static("default-src 'self'"))
                    );
                }
                
                // Add Strict-Transport-Security if configured
                if let Some(ref hsts) = config.hsts {
                    headers.insert(
                        HeaderName::from_static("strict-transport-security"),
                        HeaderValue::from_str(hsts).unwrap_or_else(|_| HeaderValue::from_static("max-age=31536000"))
                    );
                }
            }
            
            Ok(response)
        })
    }
}

/// Enhanced input validation utilities
pub mod validation {
    use super::*;
    use regex::Regex;

    /// Validate and sanitize filename
    pub fn sanitize_filename(filename: &str) -> Result<String, String> {
        if filename.is_empty() {
            return Err("Filename cannot be empty".to_string());
        }
        
        if filename.len() > 255 {
            return Err("Filename too long".to_string());
        }
        
        // Remove dangerous characters and sequences
        let sanitized = filename
            .chars()
            .filter(|c| c.is_alphanumeric() || matches!(*c, '.' | '-' | '_' | ' '))
            .collect::<String>();
        
        // Prevent directory traversal
        if sanitized.contains("..") || sanitized.starts_with('.') {
            return Err("Invalid filename pattern".to_string());
        }
        
        // Prevent reserved names on Windows
        let reserved_names = ["CON", "PRN", "AUX", "NUL", "COM1", "COM2", "COM3", "COM4", 
                             "COM5", "COM6", "COM7", "COM8", "COM9", "LPT1", "LPT2", 
                             "LPT3", "LPT4", "LPT5", "LPT6", "LPT7", "LPT8", "LPT9"];
        
        let name_without_ext = sanitized.split('.').next().unwrap_or(&sanitized).to_uppercase();
        if reserved_names.contains(&name_without_ext.as_str()) {
            return Err("Reserved filename".to_string());
        }
        
        if sanitized.is_empty() {
            return Err("Filename becomes empty after sanitization".to_string());
        }
        
        Ok(sanitized)
    }
    
    /// Validate content type
    pub fn validate_content_type(content_type: &str, allowed_types: &[&str]) -> bool {
        if allowed_types.is_empty() {
            return true; // Allow all if no restrictions
        }
        
        allowed_types.iter().any(|&allowed| {
            content_type.starts_with(allowed) || 
            (allowed.ends_with("/*") && content_type.starts_with(&allowed[..allowed.len()-1]))
        })
    }
    
    /// Check for suspicious content patterns
    pub fn check_suspicious_content(content: &[u8]) -> Vec<String> {
        let mut warnings = Vec::new();
        
        // Check for executable signatures
        if content.len() >= 2 {
            match &content[0..2] {
                [0x4D, 0x5A] => warnings.push("PE executable detected".to_string()),
                [0x7F, 0x45] if content.len() >= 4 && &content[1..4] == b"ELF" => {
                    warnings.push("ELF executable detected".to_string());
                }
                _ => {}
            }
        }
        
        // Check for script content in text files
        if let Ok(text) = std::str::from_utf8(content) {
            let script_patterns = [
                r"<script[^>]*>",
                r"javascript:",
                r"vbscript:",
                r"on\w+\s*=",
                r"eval\s*\(",
                r"exec\s*\(",
            ];
            
            for pattern in &script_patterns {
                if let Ok(regex) = Regex::new(pattern) {
                    if regex.is_match(text) {
                        warnings.push(format!("Suspicious script pattern detected: {}", pattern));
                    }
                }
            }
        }
        
        warnings
    }
    
    /// Generate content hash for integrity verification
    pub fn generate_content_hash(content: &[u8]) -> String {
        let mut hasher = Sha256::new();
        hasher.update(content);
        format!("{:x}", hasher.finalize())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use super::validation::*;

    #[test]
    fn test_sanitize_filename() {
        assert_eq!(sanitize_filename("test.txt").unwrap(), "test.txt");
        assert_eq!(sanitize_filename("test file.txt").unwrap(), "test file.txt");
        assert!(sanitize_filename("../../../etc/passwd").is_err());
        assert!(sanitize_filename("").is_err());
        assert!(sanitize_filename("CON.txt").is_err());
    }

    #[test]
    fn test_validate_content_type() {
        let allowed = vec!["text/*", "image/png"];
        assert!(validate_content_type("text/plain", &allowed));
        assert!(validate_content_type("image/png", &allowed));
        assert!(!validate_content_type("application/octet-stream", &allowed));
    }

    #[test]
    fn test_check_suspicious_content() {
        let script_content = b"<script>alert('xss')</script>";
        let warnings = check_suspicious_content(script_content);
        assert!(!warnings.is_empty());
        
        let safe_content = b"Hello, world!";
        let warnings = check_suspicious_content(safe_content);
        assert!(warnings.is_empty());
    }

    #[test]
    fn test_generate_content_hash() {
        let content = b"test content";
        let hash1 = generate_content_hash(content);
        let hash2 = generate_content_hash(content);
        assert_eq!(hash1, hash2);
        assert_eq!(hash1.len(), 64); // SHA256 hex length
    }
}
