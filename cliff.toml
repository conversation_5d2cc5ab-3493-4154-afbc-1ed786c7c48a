# git-cliff ~ configuration file
# https://git-cliff.org/docs/configuration

[changelog]
# template for the changelog header
header = """
# Changelog\n
All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).\n
"""
# template for the changelog body
# https://keats.github.io/tera/docs/#introduction
body = """
{%- macro remote_url() -%}
  https://github.com/{{ remote.github.owner }}/{{ remote.github.repo }}
{%- endmacro -%}

{% if version -%}
    ## [{{ version | trim_start_matches(pat="v") }}] - {{ timestamp | date(format="%Y-%m-%d") }}
{% else -%}
    ## [Unreleased]
{% endif -%}

{% for group, commits in commits | group_by(attribute="group") %}
    ### {{ group | upper_first }}
    {%- for commit in commits %}
        - {{ commit.message | split(pat="\n") | first | upper_first | trim }}\
            {% if commit.remote.username %} by @{{ commit.remote.username }}{%- endif -%}
            {% if commit.remote.pr_number %} in \
            [#{{ commit.remote.pr_number }}]({{ self::remote_url() }}/pull/{{ commit.remote.pr_number }}) \
            {%- endif -%}
    {% endfor %}
{% endfor %}

{%- if github.contributors | filter(attribute="is_first_time", value=true) | length != 0 %}
    ### New Contributors
{%- endif -%}
{% for contributor in github.contributors | filter(attribute="is_first_time", value=true) %}
        - @{{ contributor.username }} made their first contribution
            {%- if contributor.pr_number %} in \
            [#{{ contributor.pr_number }}]({{ self::remote_url() }}/pull/{{ contributor.pr_number }}) \
            {%- endif %}
{%- endfor %}\n\n
"""
# template for the changelog footer
footer = """
{%- macro remote_url() -%}
  https://github.com/{{ remote.github.owner }}/{{ remote.github.repo }}
{%- endmacro -%}

{% for release in releases -%}
    {% if release.version -%}
        {% if release.previous.version -%}
            [{{ release.version | trim_start_matches(pat="v") }}]: \
                {{ self::remote_url() }}/compare/{{ release.previous.version }}..{{ release.version }}
        {% endif -%}
    {% else -%}
        [unreleased]: {{ self::remote_url() }}/compare/{{ release.previous.version }}..HEAD
    {% endif -%}
{% endfor %}
<!-- generated by git-cliff -->
"""
# remove the leading and trailing whitespace from the templates
trim = true

[git]
# parse the commits based on https://www.conventionalcommits.org
conventional_commits = true
# filter out the commits that are not conventional
filter_unconventional = false
# regex for preprocessing the commit messages
commit_preprocessors = [
    # remove issue numbers from commits
    { pattern = '\((\w+\s)?#([0-9]+)\)', replace = "" },
]
# regex for parsing and grouping commits
commit_parsers = [
    { message = "^[a|A]dd", group = "Added" },
    { message = "^[s|S]upport", group = "Added" },
    { message = "^[r|R]emove", group = "Removed" },
    { message = "^.*: add", group = "Added" },
    { message = "^.*: support", group = "Added" },
    { message = "^.*: remove", group = "Removed" },
    { message = "^.*: delete", group = "Removed" },
    { message = "^test", group = "Fixed" },
    { message = "^fix", group = "Fixed" },
    { message = "^.*: fix", group = "Fixed" },
    { message = "^.*", group = "Changed" },
]
# filter out the commits that are not matched by commit parsers
filter_commits = false
# sort the tags topologically
topo_order = false
# sort the commits inside sections by oldest/newest order
sort_commits = "newest"
