# Screenshot Commands Guide for Prompt Evidence

## Overview
This guide provides the exact commands to run for taking screenshots as evidence for each implemented prompt. Each prompt needs 2 images showing the implementation working.

## Prerequisites Setup

### 1. Navigate to Project Directory
```bash
cd c:\dev\rustypaste
```

### 2. Build the Project
```bash
cargo build --release
```

## Prompt 1: Testing Infrastructure Screenshots

### Screenshot 1: Test Coverage Results
**Command to run:**
```bash
# Install coverage tool if not already installed
cargo install cargo-tarpaulin

# Generate coverage report
cargo tarpaulin --out Stdout --verbose
```

**What to capture:**
- The coverage percentage output
- Module-by-module coverage breakdown
- Total lines covered vs total lines

**Expected output should show:**
- Overall coverage >85%
- Individual module coverage percentages
- "Coverage Results" summary

### Screenshot 2: All Tests Passing
**Command to run:**
```bash
# Run all tests with detailed output
cargo test -- --nocapture --test-threads 1
```

**What to capture:**
- All test results showing "ok"
- Test count summary (e.g., "21 passed; 0 failed")
- Integration tests and unit tests both passing

**Expected output should show:**
- Multiple test functions passing
- "test result: ok" at the end
- No failures or errors

## Prompt 2: Monitoring & Metrics Screenshots

### Screenshot 1: Health Endpoints Working
**Commands to run:**

**Terminal 1 (Start server):**
```bash
cargo run --release
```

**Terminal 2 (Test endpoints):**
```bash
# Test health endpoint
curl -i http://127.0.0.1:8000/health

# Test metrics endpoint (first few lines)
curl -s http://127.0.0.1:8000/metrics | head -20
```

**What to capture:**
- Health endpoint JSON response with "status": "healthy"
- Metrics endpoint showing Prometheus format metrics
- Response times and HTTP 200 status codes

### Screenshot 2: Prometheus Metrics Output
**Command to run:**
```bash
# Get specific rustypaste metrics
curl -s http://127.0.0.1:8000/metrics | grep "rustypaste_"
```

**What to capture:**
- Multiple rustypaste metrics listed
- Different metric types (counters, gauges, histograms)
- Actual metric values

**Expected metrics to show:**
- `rustypaste_http_requests_total`
- `rustypaste_file_uploads_total`
- `rustypaste_storage_used_bytes`
- `rustypaste_memory_usage_bytes`

## Prompt 3: Security & Rate Limiting Screenshots

### Screenshot 1: Rate Limiting in Action
**Commands to run:**

**Terminal 1 (Start server):**
```bash
cargo run --release
```

**Terminal 2 (Trigger rate limiting):**
```bash
# Send rapid requests to trigger rate limiting
for i in {1..70}; do
  echo "Request $i: $(curl -s -o /dev/null -w "%{http_code}" http://127.0.0.1:8000/health)"
  sleep 0.1
done
```

**What to capture:**
- First requests showing "200" status codes
- Later requests showing "429" (Too Many Requests) status codes
- The transition from allowed to blocked requests

### Screenshot 2: Security Headers Validation
**Command to run:**
```bash
# Check security headers in response
curl -I http://127.0.0.1:8000/health
```

**What to capture:**
- HTTP response headers including:
  - `x-frame-options: DENY`
  - `x-content-type-options: nosniff`
  - `content-security-policy: default-src 'self'`
  - `strict-transport-security` (if HTTPS)

## Alternative Screenshot Commands (if above don't work)

### For Testing (Alternative):
```bash
# Simple test run
cargo test

# Coverage with different tool
cargo install cargo-llvm-cov
cargo llvm-cov --html
```

### For Monitoring (Alternative):
```bash
# Check if server is running
curl http://127.0.0.1:8000/version

# Simple metrics check
curl http://127.0.0.1:8000/metrics | wc -l
```

### For Security (Alternative):
```bash
# Test file upload with security
echo "test content" > test.txt
curl -F "file=@test.txt" http://127.0.0.1:8000/

# Check response headers on upload
curl -I -F "file=@test.txt" http://127.0.0.1:8000/
```

## Screenshot Tips

### Best Practices:
1. **Full Terminal Window**: Capture the entire terminal window
2. **Clear Output**: Make sure the command output is clearly visible
3. **Command Visible**: Include the command you ran in the screenshot
4. **Timestamp**: Terminal timestamps help show when tests were run
5. **No Errors**: Ensure no error messages are visible unless expected

### What Makes Good Evidence:
- **Prompt 1**: Test coverage >85% and all tests passing
- **Prompt 2**: Health endpoints responding and metrics being generated
- **Prompt 3**: Rate limiting working (429 errors) and security headers present

### File Naming Convention:
- `Prompt1_Testing_Coverage.png`
- `Prompt1_All_Tests_Passing.png`
- `Prompt2_Health_Endpoints.png`
- `Prompt2_Prometheus_Metrics.png`
- `Prompt3_Rate_Limiting.png`
- `Prompt3_Security_Headers.png`

## Troubleshooting

### If Server Won't Start:
```bash
# Check if port is in use
netstat -an | findstr :8000

# Kill existing process
taskkill /f /im rustypaste.exe
```

### If Tests Fail:
```bash
# Clean and rebuild
cargo clean
cargo build

# Run tests individually
cargo test config::tests
cargo test auth::tests
```

### If Coverage Tool Issues:
```bash
# Alternative coverage
cargo test --verbose

# Manual test count
find src/ -name "*.rs" -exec grep -l "#\[test\]" {} \; | wc -l
```

---

**Remember**: Take screenshots showing successful execution of these commands. The evidence should clearly demonstrate that each prompt's success metrics were achieved.
